#include "voice_hybrid_system.h"
#include <esp_log.h>
#include <LittleFS.h>
#include <vector>
#include <algorithm>

static const char* TAG = "VoiceStorage";

// 外部声明
extern String audio_path;

// 内部结构定义
typedef struct {
    String filename;
    uint32_t size;
    uint32_t last_access_time;
    uint16_t version;
    bool is_temp_file;
} file_info_t;

// 配置常量
#define MIN_FREE_SPACE_KB           512     // 最小保留空间 512KB
#define CLEANUP_THRESHOLD_PERCENT   85      // 清理阈值 85%
#define MAX_TEMP_FILE_AGE_MS        300000  // 临时文件最大存活时间 5分钟
#define DEFRAG_THRESHOLD_PERCENT    70      // 碎片整理阈值 70%

// 内部函数声明
static voice_error_t scan_audio_files(std::vector<file_info_t>& files);
static voice_error_t cleanup_temp_files(void);
static voice_error_t cleanup_old_versions(void);
static voice_error_t cleanup_least_used_files(uint32_t target_free_bytes);
static voice_error_t defragment_storage(void);
static uint32_t get_storage_usage_percent(void);
static bool should_cleanup_file(const file_info_t& file);
static int compare_files_by_priority(const file_info_t& a, const file_info_t& b);

/**
 * @brief 监控存储空间状态
 */
voice_error_t voice_monitor_storage(void)
{
    ESP_LOGD(TAG, "Monitoring storage space...");

    uint32_t total_bytes = LittleFS.totalBytes();
    uint32_t used_bytes = LittleFS.usedBytes();
    uint32_t free_bytes = total_bytes - used_bytes;
    uint32_t usage_percent = (used_bytes * 100) / total_bytes;

    ESP_LOGI(TAG, "Storage: %d%% used (%d/%d bytes, %d free)", 
             usage_percent, used_bytes, total_bytes, free_bytes);

    // 检查是否需要清理
    if (usage_percent >= CLEANUP_THRESHOLD_PERCENT || 
        free_bytes < (MIN_FREE_SPACE_KB * 1024)) {
        
        ESP_LOGW(TAG, "Storage usage high, starting cleanup...");
        return voice_cleanup_storage();
    }

    // 检查是否需要碎片整理
    if (usage_percent >= DEFRAG_THRESHOLD_PERCENT) {
        ESP_LOGI(TAG, "Storage fragmentation may be high, considering defragmentation");
        // 注意：LittleFS通常不需要手动碎片整理，这里主要是监控
    }

    return VOICE_ERR_OK;
}

/**
 * @brief 清理存储空间
 */
voice_error_t voice_cleanup_storage(void)
{
    ESP_LOGI(TAG, "Starting storage cleanup...");

    voice_error_t err;
    uint32_t initial_free = LittleFS.totalBytes() - LittleFS.usedBytes();

    // 1. 清理临时文件
    err = cleanup_temp_files();
    if (err != VOICE_ERR_OK) {
        ESP_LOGW(TAG, "Failed to cleanup temp files: %d", err);
    }

    // 2. 清理旧版本文件
    err = cleanup_old_versions();
    if (err != VOICE_ERR_OK) {
        ESP_LOGW(TAG, "Failed to cleanup old versions: %d", err);
    }

    // 3. 检查是否还需要更多空间
    uint32_t current_free = LittleFS.totalBytes() - LittleFS.usedBytes();
    uint32_t usage_percent = get_storage_usage_percent();

    if (usage_percent >= CLEANUP_THRESHOLD_PERCENT || 
        current_free < (MIN_FREE_SPACE_KB * 1024)) {
        
        ESP_LOGW(TAG, "Still need more space, cleaning least used files...");
        uint32_t target_free = MIN_FREE_SPACE_KB * 1024 * 2; // 目标释放2倍最小空间
        err = cleanup_least_used_files(target_free);
        if (err != VOICE_ERR_OK) {
            ESP_LOGE(TAG, "Failed to cleanup least used files: %d", err);
        }
    }

    uint32_t final_free = LittleFS.totalBytes() - LittleFS.usedBytes();
    uint32_t freed_bytes = final_free - initial_free;

    ESP_LOGI(TAG, "Storage cleanup completed, freed %d bytes", freed_bytes);
    return VOICE_ERR_OK;
}

/**
 * @brief 扫描音频文件
 */
static voice_error_t scan_audio_files(std::vector<file_info_t>& files)
{
    files.clear();

    File root = LittleFS.open(audio_path);
    if (!root || !root.isDirectory()) {
        ESP_LOGE(TAG, "Failed to open audio directory");
        return VOICE_ERR_INIT_FAILED;
    }

    File file = root.openNextFile();
    while (file) {
        if (!file.isDirectory()) {
            String filename = file.name();
            
            // 跳过版本文件
            if (!filename.startsWith(".")) {
                file_info_t info;
                info.filename = filename;
                info.size = file.size();
                info.last_access_time = file.getLastWrite(); // 使用修改时间作为访问时间
                info.is_temp_file = filename.startsWith("temp_");
                info.version = 1; // 默认版本

                // 尝试获取版本信息
                if (filename.endsWith(".wav")) {
                    String base_name = filename.substring(0, filename.length() - 4);
                    String version_file = audio_path + "/." + base_name + ".version";
                    File ver_file = LittleFS.open(version_file, "r");
                    if (ver_file) {
                        info.version = ver_file.readString().toInt();
                        ver_file.close();
                    }
                }

                files.push_back(info);
            }
        }
        file = root.openNextFile();
    }

    ESP_LOGD(TAG, "Scanned %d audio files", files.size());
    return VOICE_ERR_OK;
}

/**
 * @brief 清理临时文件
 */
static voice_error_t cleanup_temp_files(void)
{
    ESP_LOGI(TAG, "Cleaning up temporary files...");

    std::vector<file_info_t> files;
    voice_error_t err = scan_audio_files(files);
    if (err != VOICE_ERR_OK) {
        return err;
    }

    uint32_t current_time = millis();
    int cleaned_count = 0;
    uint32_t freed_bytes = 0;

    for (const auto& file : files) {
        if (file.is_temp_file) {
            // 检查文件年龄
            uint32_t file_age = current_time - file.last_access_time;
            if (file_age > MAX_TEMP_FILE_AGE_MS) {
                String filepath = audio_path + "/" + file.filename;
                if (LittleFS.remove(filepath)) {
                    ESP_LOGD(TAG, "Removed temp file: %s (%d bytes)", 
                             file.filename.c_str(), file.size);
                    cleaned_count++;
                    freed_bytes += file.size;
                } else {
                    ESP_LOGW(TAG, "Failed to remove temp file: %s", file.filename.c_str());
                }
            }
        }
    }

    ESP_LOGI(TAG, "Cleaned %d temp files, freed %d bytes", cleaned_count, freed_bytes);
    return VOICE_ERR_OK;
}

/**
 * @brief 清理旧版本文件
 */
static voice_error_t cleanup_old_versions(void)
{
    ESP_LOGI(TAG, "Cleaning up old version files...");

    std::vector<file_info_t> files;
    voice_error_t err = scan_audio_files(files);
    if (err != VOICE_ERR_OK) {
        return err;
    }

    // 按文件名分组，保留最新版本
    std::map<String, std::vector<file_info_t>> file_groups;
    
    for (const auto& file : files) {
        if (file.filename.endsWith(".wav") && !file.is_temp_file) {
            String base_name = file.filename.substring(0, file.filename.length() - 4);
            file_groups[base_name].push_back(file);
        }
    }

    int cleaned_count = 0;
    uint32_t freed_bytes = 0;

    // 处理每个文件组
    for (auto& group : file_groups) {
        auto& file_list = group.second;
        
        if (file_list.size() > 1) {
            // 按版本排序，保留最新的
            std::sort(file_list.begin(), file_list.end(),
                     [](const file_info_t& a, const file_info_t& b) {
                         return a.version > b.version;
                     });

            // 删除旧版本（保留第一个，即最新版本）
            for (size_t i = 1; i < file_list.size(); i++) {
                const auto& old_file = file_list[i];
                String filepath = audio_path + "/" + old_file.filename;
                
                if (LittleFS.remove(filepath)) {
                    ESP_LOGD(TAG, "Removed old version: %s v%d (%d bytes)", 
                             old_file.filename.c_str(), old_file.version, old_file.size);
                    cleaned_count++;
                    freed_bytes += old_file.size;

                    // 同时删除对应的版本文件
                    String version_file = audio_path + "/." + group.first + ".version";
                    LittleFS.remove(version_file);
                } else {
                    ESP_LOGW(TAG, "Failed to remove old version: %s", old_file.filename.c_str());
                }
            }
        }
    }

    ESP_LOGI(TAG, "Cleaned %d old version files, freed %d bytes", cleaned_count, freed_bytes);
    return VOICE_ERR_OK;
}

/**
 * @brief 清理最少使用的文件
 */
static voice_error_t cleanup_least_used_files(uint32_t target_free_bytes)
{
    ESP_LOGI(TAG, "Cleaning up least used files, target: %d bytes", target_free_bytes);

    std::vector<file_info_t> files;
    voice_error_t err = scan_audio_files(files);
    if (err != VOICE_ERR_OK) {
        return err;
    }

    // 过滤出可以清理的文件
    std::vector<file_info_t> cleanable_files;
    for (const auto& file : files) {
        if (should_cleanup_file(file)) {
            cleanable_files.push_back(file);
        }
    }

    if (cleanable_files.empty()) {
        ESP_LOGW(TAG, "No cleanable files found");
        return VOICE_ERR_OK;
    }

    // 按优先级排序（最不重要的在前面）
    std::sort(cleanable_files.begin(), cleanable_files.end(), compare_files_by_priority);

    uint32_t freed_bytes = 0;
    int cleaned_count = 0;

    for (const auto& file : cleanable_files) {
        if (freed_bytes >= target_free_bytes) {
            break;
        }

        String filepath = audio_path + "/" + file.filename;
        if (LittleFS.remove(filepath)) {
            ESP_LOGD(TAG, "Removed least used file: %s (%d bytes)", 
                     file.filename.c_str(), file.size);
            freed_bytes += file.size;
            cleaned_count++;

            // 删除对应的版本文件
            if (file.filename.endsWith(".wav")) {
                String base_name = file.filename.substring(0, file.filename.length() - 4);
                String version_file = audio_path + "/." + base_name + ".version";
                LittleFS.remove(version_file);
            }
        } else {
            ESP_LOGW(TAG, "Failed to remove file: %s", file.filename.c_str());
        }
    }

    ESP_LOGI(TAG, "Cleaned %d least used files, freed %d bytes", cleaned_count, freed_bytes);
    return VOICE_ERR_OK;
}
