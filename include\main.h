#ifndef __MAIN_H__
#define __MAIN_H__

#include <Arduino.h>
#include "gpio.h"
#include "gui_guider.h"

#define MAX_USER_NUMBER 10
#define MAX_WIFI_NUMBER 10
#define MAX_MESSAGE_NUMBER 20
#define MAX_BLE_TRUST_NUMBER 30
#define POP_DISPLAY_TIME 5
#define POP_DISPLAY_LONG_TIME 15
#define BASE_FILE_PATH "/HealthHub"

// #define LIGHT_ON false
// #define LIGHT_OFF true

#define LIGHT_ON true
#define LIGHT_OFF false

// Code Security
#include <atomic>
#include <array>

// 互斥锁声明
extern SemaphoreHandle_t xWifiMutex;
extern SemaphoreHandle_t xUserInfoMutex;
extern SemaphoreHandle_t xMqttStatusMutex;
extern SemaphoreHandle_t xGuiMutex;
extern portMUX_TYPE wifiStateMux;

// 间隔time ms运行一次func
#define CM_EXECUTE_INTERVAL(time, func)    \
    do                                     \
    {                                      \
        static unsigned long lasttime = 0; \
        if (millis() - lasttime >= (time)) \
        {                                  \
            func;                          \
            lasttime = millis();           \
        }                                  \
    } while (0)

// 间隔time ms运行一次func
#define CM_EXECUTE_INTERVAL_DELAY_FIRST(time, func) \
    do                                              \
    {                                               \
        static unsigned long lasttime = 0;          \
        static unsigned char first_run = 0;         \
        if (first_run == 0)                         \
        {                                           \
            first_run = 1;                          \
            lasttime = millis();                    \
        }                                           \
        if (millis() - lasttime >= (time))          \
        {                                           \
            func;                                   \
            lasttime = millis();                    \
        }                                           \
    } while (0)

// 延迟time s运行func
#define CM_EXECUTE_DELAY_FUNC(time, func)   \
    do                                      \
    {                                       \
        static unsigned char timecount = 0; \
        if (++timecount > time)             \
        {                                   \
            func;                           \
            timecount = time;               \
        }                                   \
    } while (0)

class Throttle
{
private:
    uint32_t last_call_time;
    uint32_t delay;

public:
    Throttle(uint32_t delay_ms) : last_call_time(0), delay(delay_ms) {}

    bool should_run()
    {
        uint32_t now = millis();
        if (now - last_call_time >= delay)
        {
            last_call_time = now;
            return true;
        }
        return false;
    }
};

class UIUpdater
{
private:
    S_UpdatingInfo info;
    Throttle throttle;

    void setInfo(const char *new_info)
    {
        if (new_info == nullptr)
        {
            return;
        }

        char *temp = new (std::nothrow) char[strlen(new_info) + 1];
        if (temp == nullptr)
        {
            log_e("内存分配失败");
            return;
        }

        strcpy(temp, new_info);

        if (info.info != nullptr)
        {
            delete[] info.info;
        }

        info.info = temp;
    }

public:
    UIUpdater(uint32_t throttle_ms = 500) : throttle(throttle_ms)
    {
        info.info = nullptr;
        info.progress = 0;
    }

    ~UIUpdater()
    {
        if (info.info != nullptr)
        {
            delete[] info.info;
        }
    }

    void update(const char *new_info = nullptr, uint8_t progress = 0)
    {
        bool should_update = false;

        if (new_info != nullptr && (info.info == nullptr || strcmp(info.info, new_info) != 0))
        {
            setInfo(new_info);
            should_update = true;
        }

        if (progress != info.progress)
        {
            info.progress = progress;
            should_update = true;
        }

        if (should_update || throttle.should_run())
        {
            info_modify_Updating(info);
        }
    }
};

// user info
struct UserInfo
{
    String name;
    uint16_t id;
    String type;
    String url;
};

// user info
struct BleTrustInfo
{
    String addr;
    uint16_t type;
    uint16_t upload_type;
    String model;
    String manufacturer;
};

// wifi
struct WifiData
{
    String ssid;
    String psw;
};

// message
struct MessageEvent
{
    String title;
    String body;
    String time;
};

// config
struct DeviceConfig
{
    int8_t timezone;
};

// push message
struct PubMessage
{
    String fielf_name;
    uint16_t field_value;
    String measured_at;
};

// 血氧数据
typedef struct
{
    uint8_t PulseRate;
    uint8_t SPO2;
    uint8_t PIData;
    char time[17];
} S_BloodOxygen;

// 血压数据
typedef struct
{
    uint8_t PressH;
    uint8_t PressL;
    uint8_t HeartBeat;
    uint8_t Systolic;
    uint8_t Diastolic;
    uint8_t Pulse;
    uint8_t HeartAnomaly;
    uint8_t DetectMode;
    char time[17];
} S_BloodPressure;

typedef struct
{
    uint8_t cur_number;
    bool inTrustList;
} S_TrustListCheck;
// 体重数据
enum E_Unit
{
    KG,
    LB,
    STLB,
    JIN,
    G,
    LB_OZ,
    OZ,
    ML_WATER,
    ML_MILK,
    FL_OZ_WATER,
    FL_OZ_MILK,
    ST,
};

typedef struct
{
    uint8_t WeightH;
    uint8_t WeightL;
    E_Unit Unit;
    uint8_t ImpedanceL;
    uint8_t ImpedanceM;
    uint8_t ImpedanceH;
    char time[17];
} S_BodyWeight;

typedef struct
{
    uint8_t TempH;
    uint8_t TempL;
    uint8_t DetectCnt;
    char time[17];
} S_TemperatureAdv;

typedef struct
{
    uint8_t Glucose;
    uint8_t DetectCnt;
    uint8_t mealState;
    char time[17];
} S_BloodGlucoseAdv;

typedef struct
{
    S_BloodOxygen oxygen;
    S_BloodPressure pressure;
    S_BodyWeight weight;
    S_TemperatureAdv temperature;
    S_BloodGlucoseAdv glucose;
} S_VitalsData;
// 0:初始状态；1：开始wifi扫描； 2：获取最强信号的wifi；3：wifi连接
enum E_WifiScanSta
{
    WIFI_SCAN_DEFAULT,
    WIFI_SCAN_START,
    WIFI_SCAN_START_CONNECT,
    WIFI_SCAN_CONNECTING,
};
// 0:disconnect,1:connected,2:connecting,3:smartconfig,4:first connect
enum E_WifiSta
{
    WIFI_DISCONNECTED,
    WIFI_CONNECTED,
    WIFI_CONNECTING,
    WIFI_SMARTCONFIG,
    WIFI_FIRST_CONNECTED,
    WIFI_RECONNECT_SMARTCONFIG,
    WIFI_RECONNECT_CONNECTED,
};
// 0:disconnect,1:connecting,2:connected
enum E_ServerSta
{
    SERVER_DISCONNECTED,
    SERVER_CONNECTING,
    SERVER_CONNECTED,
};
// 0:disconnect,1:connecting,2:connected
enum E_MqttSta
{
    MQTT_CLIENT_DISCONNECTED,
    MQTT_CLIENT_CONNECTING,
    MQTT_CLIENT_CONNECTED,
    MQTT_DEFAULT_CLIENT_CONNECTED,
    MQTT_CLIENT_RECONNECT_NEEDED,
};
// 蓝牙状态标志位 0：无操作；1：进行wifi连接；2：进行蓝牙广播；3：进行蓝牙扫描
enum E_BleSta
{
    BLE_NO_OPERATION,
    BLE_WIFI_CONNECT,
    BLE_BROADCAST,
    BLE_SCANNING,
};

enum E_AUDIO_FILE
{
    OPEN_APP_TO_CONFIG,
    NETWORK_SUCCESS,
    SELECT_USER,
    BLOOD_PRESSURE_DATA,
    TEMPERATURE_DATA,
    WEIGHT_DATA,
    BLOOD_GLUCOSE_DATA,
    BLOOD_OXYGEN_DATA,
    TAP_SMART_CONFIG,
    NEW_MESSAGE
};

enum E_DATA_TYPE
{
    DATA_TYPE_NONE,
    DATA_TYPE_WEIGHT,
    DATA_TYPE_BLOOD_GLUCOSE,
    DATA_TYPE_BLOOD_OXYGEN,
    DATA_TYPE_BLOOD_PRESSURE,
    DATA_TYPE_TEMPERATURE,
};

// 将关键状态变量声明为原子类型
extern std::atomic<E_WifiSta> WifiState;
extern std::atomic<E_ServerSta> ServerState;
extern std::atomic<E_MqttSta> MqttState;
extern std::atomic<E_WifiScanSta> multi_connect_flag;

// 将原来的WifiData数组和计数器改为原子类型
extern std::array<WifiData, MAX_WIFI_NUMBER> wifiInfo;
extern std::atomic<uint8_t> wifiStorageCnt;

// WiFi信息访问函数声明
void setWifiInfo(uint8_t index, const String &ssid, const String &psw);
WifiData getWifiInfo(uint8_t index);
uint8_t getWifiStorageCount();
void setWifiStorageCount(uint8_t count);
void incrementWifiStorageCount();
void decrementWifiStorageCount();
void clearWifiStorage();

#endif
