#!/usr/bin/env python3
"""
PlatformIO构建脚本集成
在编译时自动生成语音ROM文件
"""

import os
import sys
import subprocess
from pathlib import Path

# 添加当前脚本目录到Python路径
script_dir = Path(__file__).parent
sys.path.insert(0, str(script_dir))

from embed_voice_files import VoiceROMBuilder, VOICE_COMPRESSION_ADPCM

def build_voice_rom(source, target, env):
    """PlatformIO构建钩子函数"""
    print("Building voice ROM...")
    
    # 获取项目根目录
    project_dir = env.get("PROJECT_DIR")
    voice_files_dir = os.path.join(project_dir, "voice_files")
    voice_list_file = os.path.join(voice_files_dir, "voice_list.txt")
    output_file = os.path.join(project_dir, "src", "voice_rom.bin")
    
    # 检查语音文件目录是否存在
    if not os.path.exists(voice_files_dir):
        print(f"Warning: Voice files directory not found: {voice_files_dir}")
        print("Creating empty voice ROM...")
        create_empty_rom(output_file)
        return
    
    # 检查语音文件列表是否存在
    if not os.path.exists(voice_list_file):
        print(f"Warning: Voice list file not found: {voice_list_file}")
        print("Scanning for .wav files in voice_files directory...")
        create_rom_from_directory(voice_files_dir, output_file)
        return
    
    # 从文件列表构建ROM
    create_rom_from_list(voice_files_dir, voice_list_file, output_file)

def create_empty_rom(output_file):
    """创建空的ROM文件"""
    builder = VoiceROMBuilder()
    
    # 创建一个最小的ROM文件
    if builder.build_rom(output_file):
        print(f"Empty voice ROM created: {output_file}")
    else:
        print("Failed to create empty voice ROM")

def create_rom_from_directory(voice_files_dir, output_file):
    """从目录扫描创建ROM"""
    builder = VoiceROMBuilder()
    
    # 扫描.wav文件
    voice_dir = Path(voice_files_dir)
    wav_files = list(voice_dir.glob("*.wav"))
    
    if not wav_files:
        print("No .wav files found, creating empty ROM")
        create_empty_rom(output_file)
        return
    
    # 添加文件到ROM
    for wav_file in wav_files:
        builder.add_file(str(wav_file), VOICE_COMPRESSION_ADPCM)
    
    # 构建ROM
    if builder.build_rom(output_file):
        print(f"Voice ROM created from directory: {output_file}")
    else:
        print("Failed to create voice ROM from directory")

def create_rom_from_list(voice_files_dir, voice_list_file, output_file):
    """从文件列表创建ROM"""
    builder = VoiceROMBuilder()
    
    try:
        with open(voice_list_file, 'r') as f:
            for line in f:
                filename = line.strip()
                if filename and not filename.startswith('#'):
                    wav_file = os.path.join(voice_files_dir, filename + ".wav")
                    if os.path.exists(wav_file):
                        builder.add_file(wav_file, VOICE_COMPRESSION_ADPCM)
                    else:
                        print(f"Warning: Voice file not found: {wav_file}")
    except Exception as e:
        print(f"Error reading voice list file: {e}")
        create_empty_rom(output_file)
        return
    
    # 构建ROM
    if builder.build_rom(output_file):
        print(f"Voice ROM created from list: {output_file}")
    else:
        print("Failed to create voice ROM from list")

def main():
    """独立运行时的主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='Build voice ROM for BLE Gateway')
    parser.add_argument('--project-dir', required=True, help='Project directory')
    parser.add_argument('--output', help='Output ROM file')
    
    args = parser.parse_args()
    
    voice_files_dir = os.path.join(args.project_dir, "voice_files")
    voice_list_file = os.path.join(voice_files_dir, "voice_list.txt")
    output_file = args.output or os.path.join(args.project_dir, "src", "voice_rom.bin")
    
    # 模拟环境变量
    class MockEnv:
        def get(self, key):
            if key == "PROJECT_DIR":
                return args.project_dir
            return None
    
    build_voice_rom(None, None, MockEnv())

if __name__ == '__main__':
    main()
